# Copy this to config.ini and fill in your actual values
# NOTE: Environment variables (.env file) take precedence over config.ini values

[proxy]
# Proxy Configuration
host = proxy.example.com
user = your_proxy_username
pass = your_proxy_password

[snow]
# ServiceNow Configuration
user_email = <EMAIL>
user_password = your_password
homepage_url = https://your-instance.service-now.com
saml_acs_url = https://your-instance.service-now.com/navpage.do
referer = https://your-instance.service-now.com

# Legacy single report configuration (used as fallback for Report 1)
report_url = https://your-instance.service-now.com/api/now/table/incident
report_payload = {"short_description": "Test incident"}

[ssl]
# SSL Configuration
disable_warnings = true
verify = false

# IMPORTANT: For multi-report configuration, use the .env file instead
# The .env file supports configuring 2 separate reports with different URLs, payloads, and output files
# See .env file for SNOW_REPORT1_* and SNOW_REPORT2_* configuration options